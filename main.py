import os
import threading
from typing import List

from confluent_kafka import Consumer
from prometheus_client import start_http_server

from src.background_jobs.clean_up_outdated_data import run_daily_cleanup_async
from src.common.consts import EMBEDDING_MODEL_NAME, REQUEST_KAFKA_BOOTSTRAP_SERVERS, REQUEST_KAFKA_CONSUMER_GROUP, \
    REQUEST_KAFKA_USERNAME, REQUEST_KAFKA_PASSWORD, REQUEST_KAFKA_TOPIC, LOGGER_KAFKA_BOOTSTRAP_SERVERS, \
    LOGGER_KAFKA_USERNAME, LOGGER_KAFKA_TOPIC, LOGGER_KAFKA_PASSWORD, INSTANCE_ID, \
    CANCEL_API_SCHEME, CANCEL_API_HOST, CANCEL_API_PATH, CANCEL_API_TIMEOUT_MS, REQUEST_KAFKA_SECURITY_PROTOCOL
from src.data_structures.embedding_encoder import BGEM3FlagModelEmbeddingEncoder
from src.exporters.base import Exporter
from src.exporters.logger import LoggerExporter
from src.exporters.cancel_order import CancelOrderExporter
from src.processors.address_clusterer import AgglomerativeAddressClusterer
from src.processors.address_parser import NERAddressParser
from src.processors.embedding_generator import AddressEmbeddingGenerator
from src.processors.realtime_processor import RealtimeProcessor
from src.processors.startup_processor import StartupProcessor
from src.utils.config_manager import ConfigManager
from src.utils.kafka_config import build_kafka_consumer_config
from src.utils.logger import logger

address_parser = NERAddressParser()
embedding_generator = AddressEmbeddingGenerator(BGEM3FlagModelEmbeddingEncoder(EMBEDDING_MODEL_NAME))
address_clusterer = AgglomerativeAddressClusterer(address_parser, embedding_generator)

consumer = Consumer(
    build_kafka_consumer_config(
        bootstrap_servers=REQUEST_KAFKA_BOOTSTRAP_SERVERS,
        group_id=f'{REQUEST_KAFKA_CONSUMER_GROUP}_{INSTANCE_ID}',  # To make each instance has its own CG.
        security_protocol=REQUEST_KAFKA_SECURITY_PROTOCOL,
        username=REQUEST_KAFKA_USERNAME,
        password=REQUEST_KAFKA_PASSWORD,
        auto_offset_reset='latest',
    )
)


def init_exporters() -> List[Exporter]:
    exporters = []

    # Initialize logger exporter
    logger_exporter = LoggerExporter(bootstrap_servers=LOGGER_KAFKA_BOOTSTRAP_SERVERS, topic=LOGGER_KAFKA_TOPIC,
                                     username=LOGGER_KAFKA_USERNAME, password=LOGGER_KAFKA_PASSWORD)
    exporters.append(logger_exporter)

    # Initialize cancel order exporter if required configuration is available
    strategy_code = ConfigManager.get_instance().cancel_api_strategy_code
    cancel_api_id = ConfigManager.get_instance().cancel_api_id
    cancel_api_secret = ConfigManager.get_instance().cancel_api_secret
    if CANCEL_API_SCHEME and CANCEL_API_HOST and CANCEL_API_PATH and strategy_code and cancel_api_id and cancel_api_secret:
        cancel_order_exporter = CancelOrderExporter(
            api_scheme=CANCEL_API_SCHEME,
            api_host=CANCEL_API_HOST,
            api_path=CANCEL_API_PATH,
            strategy_code=strategy_code,
            timeout_ms=CANCEL_API_TIMEOUT_MS,
            api_id=cancel_api_id,
            api_secret=cancel_api_secret
        )
        exporters.append(cancel_order_exporter)

    return exporters


def start_prometheus_server(port=8000):
    """
    Start Prometheus metrics HTTP server in a separate thread.

    Args:
        port: The port to expose metrics on, defaults to 8000
    """
    try:
        # Use environment variable if available, otherwise use default
        metrics_port = int(os.environ.get('PROMETHEUS_PORT', port))
        start_http_server(metrics_port)
        logger.info(f"Started Prometheus metrics server on port {metrics_port}")
    except Exception as e:
        logger.error(f"Failed to start Prometheus metrics server: {e}")


def main():
    # Start Prometheus metrics server
    prometheus_thread = threading.Thread(target=start_prometheus_server, daemon=True)
    prometheus_thread.start()

    run_daily_cleanup_async(
        clusterer=address_clusterer,
    )

    startup_processor = StartupProcessor(
        address_parser=address_parser,
        embedding_generator=embedding_generator,
        address_clusterer=address_clusterer,
    )
    startup_processor.process()

    realtime_processor = RealtimeProcessor(
        address_clusterer=address_clusterer,
        consumer=consumer,
        filtered_order_topic=REQUEST_KAFKA_TOPIC,
    )
    exporters: list[Exporter] = init_exporters()
    for exporter in exporters:
        realtime_processor.add_observer(exporter)
    realtime_processor.start()


if __name__ == '__main__':
    main()
