import unittest
from unittest.mock import patch, MagicMock

from src.data_structures.request import Request
from src.data_structures.cluster_result import ClusterResult
from src.exporters.cancel_order import CancelOrderExporter


class TestCancelOrderExporter(unittest.TestCase):
    def setUp(self):
        self.api_scheme = "https"
        self.api_host = "test-api.shopee.io"
        self.api_path = "/api/v5/anti-fraud/notify_illegal_order"
        self.strategy_code = "TEST_STRATEGY_CODE"
        self.timeout_ms = 5000
        self.api_id = "1013"
        self.api_secret = "test_secret_key"
        self.exporter = CancelOrderExporter(
            api_scheme=self.api_scheme,
            api_host=self.api_host,
            api_path=self.api_path,
            strategy_code=self.strategy_code,
            timeout_ms=self.timeout_ms,
            api_id=self.api_id,
            api_secret=self.api_secret
        )
        # The full endpoint URL that should be constructed
        self.api_endpoint = f"{self.api_scheme}://{self.api_host}{self.api_path}"

        # Create test request and result
        self.request = Request(
            request_id="test_req_id_1",
            event_timestamp=1743674030,
            shopee_user_id=123,
            now_user_id=456,
            total_discount=1000,
            restaurant_id=789,
            delivery_address="Test Address",
            order_id="12345"
        )

        # Create test result with count above threshold
        self.result_above_threshold = ClusterResult(
            request_id="test_req_id_2",
            same_cluster_restaurant_user_count=6,
            request_ids_in_cluster=["test_req_id_2", "other_req_id"]
        )

        # Create test result with count below threshold
        self.result_below_threshold = ClusterResult(
            request_id="test_req_id_3",
            same_cluster_restaurant_user_count=4,
            request_ids_in_cluster=["test_req_id_3"]
        )

        # Create test result with count equal threshold
        self.result_equal_min_threshold = ClusterResult(
            request_id="test_req_id_4",
            same_cluster_restaurant_user_count=5,
            request_ids_in_cluster=["test_req_id_4"]
        )

    @patch('src.exporters.cancel_order.time.time')
    @patch('src.exporters.cancel_order.requests.post')
    @patch('src.exporters.cancel_order.ConfigManager.get_instance')
    def test_export_above_threshold_success(self, mock_config_manager, mock_post, mock_time):
        # Setup mock response and time
        mock_response = MagicMock()
        mock_response.status_code = 200
        mock_response.json.return_value = {"msg": "", "code": 0, "data": None}
        mock_post.return_value = mock_response
        mock_time.return_value = 1744706174

        # Mock the HMAC signature generation
        with patch.object(self.exporter, '_generate_hmac_signature', return_value='test_signature'):
            # Call export method
            self.exporter.export((self.request, self.result_above_threshold))

            # Verify API was called with correct parameters
            mock_post.assert_called_once_with(
                self.api_endpoint,
                headers={
                    'X-FOODY-AUTHORIZATION': 'test_signature',
                    'X-Foody-App-Id': self.api_id,
                    'Host': self.api_host,
                    'Timestamp': '1744706174',
                    'Content-Type': 'application/json',
                    'X-Request-Id': self.request.request_id
                },
                json={"order_id": self.request.order_id, "strategy_code": self.strategy_code},
                timeout=self.timeout_ms / 1000.0
            )

    @patch('src.exporters.cancel_order.requests.post')
    @patch('src.exporters.cancel_order.CANCEL_ORDER_MIN_CLUSTER_ID_COUNT_THRESHOLD', 5)
    def test_export_below_threshold_no_api_call(self, mock_post):
        # Call export method with result below threshold
        self.exporter.export((self.request, self.result_below_threshold))

        # Verify API was not called
        mock_post.assert_not_called()

    @patch('src.exporters.cancel_order.time.time')
    @patch('src.exporters.cancel_order.requests.post')
    @patch('src.exporters.cancel_order.CANCEL_ORDER_MIN_CLUSTER_ID_COUNT_THRESHOLD', 5)
    def test_export_equal_min_threshold_api_call(self, mock_post, mock_time):
        # Setup mock response and time
        mock_response = MagicMock()
        mock_response.status_code = 200
        mock_response.json.return_value = {"msg": "", "code": 0, "data": None}
        mock_post.return_value = mock_response
        mock_time.return_value = 1744706174

        # Mock the HMAC signature generation
        with patch.object(self.exporter, '_generate_hmac_signature', return_value='test_signature'):
            # Call export method with result equal to min threshold
            self.exporter.export((self.request, self.result_equal_min_threshold))

            # Verify API was called with correct parameters
            mock_post.assert_called_once_with(
                self.api_endpoint,
                headers={
                    'X-FOODY-AUTHORIZATION': 'test_signature',
                    'X-Foody-App-Id': self.api_id,
                    'Host': self.api_host,
                    'Timestamp': '1744706174',
                    'Content-Type': 'application/json',
                    'X-Request-Id': self.request.request_id
                },
                json={"order_id": self.request.order_id, "strategy_code": self.strategy_code},
                timeout=self.timeout_ms / 1000.0
            )

    @patch('src.exporters.cancel_order.time.time')
    @patch('src.exporters.cancel_order.requests.post')
    @patch('src.exporters.cancel_order.CANCEL_ORDER_MIN_CLUSTER_ID_COUNT_THRESHOLD', 5)
    def test_export_business_error_response(self, mock_post, mock_time):
        # Setup mock response with business error and time
        mock_response = MagicMock()
        mock_response.status_code = 200
        mock_response.json.return_value = {"msg": "Invalid order", "code": 1, "data": {"error_code": 23}}
        mock_post.return_value = mock_response
        mock_time.return_value = 1744706174

        # Mock the HMAC signature generation
        with patch.object(self.exporter, '_generate_hmac_signature', return_value='test_signature'):
            # Call export method
            self.exporter.export((self.request, self.result_above_threshold))

            # Verify API was called
            mock_post.assert_called_once()

    @patch('src.exporters.cancel_order.time.time')
    @patch('src.exporters.cancel_order.requests.post')
    @patch('src.exporters.cancel_order.CANCEL_ORDER_MIN_CLUSTER_ID_COUNT_THRESHOLD', 5)
    def test_export_system_error_response(self, mock_post, mock_time):
        # Setup mock response with system error and time
        mock_response = MagicMock()
        mock_response.status_code = 200
        mock_response.json.return_value = {"msg": "System error", "code": 2, "data": {"error_code": 500}}
        mock_post.return_value = mock_response
        mock_time.return_value = 1744706174

        # Mock the HMAC signature generation
        with patch.object(self.exporter, '_generate_hmac_signature', return_value='test_signature'):
            # Call export method
            self.exporter.export((self.request, self.result_above_threshold))

            # Verify API was called
            mock_post.assert_called_once()

    @patch('src.exporters.cancel_order.time.time')
    @patch('src.exporters.cancel_order.requests.post')
    @patch('src.exporters.cancel_order.CANCEL_ORDER_MIN_CLUSTER_ID_COUNT_THRESHOLD', 5)
    def test_export_null_data_response(self, mock_post, mock_time):
        # Setup mock response with null data and time
        mock_response = MagicMock()
        mock_response.status_code = 200
        mock_response.json.return_value = {"msg": "Error occurred", "code": 3, "data": None}
        mock_post.return_value = mock_response
        mock_time.return_value = 1744706174

        # Mock the HMAC signature generation
        with patch.object(self.exporter, '_generate_hmac_signature', return_value='test_signature'):
            # Call export method
            self.exporter.export((self.request, self.result_above_threshold))

            # Verify API was called
            mock_post.assert_called_once()

    @patch('src.exporters.cancel_order.time.time')
    @patch('src.exporters.cancel_order.requests.post')
    @patch('src.exporters.cancel_order.CANCEL_ORDER_MIN_CLUSTER_ID_COUNT_THRESHOLD', 5)
    def test_export_http_error(self, mock_post, mock_time):
        # Setup mock response with HTTP error and time
        mock_response = MagicMock()
        mock_response.status_code = 500
        mock_response.text = "Internal Server Error"
        mock_post.return_value = mock_response
        mock_time.return_value = 1744706174

        # Mock the HMAC signature generation
        with patch.object(self.exporter, '_generate_hmac_signature', return_value='test_signature'):
            # Call export method
            self.exporter.export((self.request, self.result_above_threshold))

            # Verify API was called
            mock_post.assert_called_once()

    @patch('src.exporters.cancel_order.requests.post')
    @patch('src.exporters.cancel_order.CANCEL_ORDER_MIN_CLUSTER_ID_COUNT_THRESHOLD', 5)
    def test_export_no_order_id(self, mock_post):
        # Create request with no order_id
        request_no_order = Request(
            request_id="test_req_id",
            event_timestamp=1743674030,
            shopee_user_id=123,
            now_user_id=456,
            total_discount=1000,
            restaurant_id=789,
            delivery_address="Test Address"
        )

        # Call export method
        self.exporter.export((request_no_order, self.result_above_threshold))

        # Verify API was not called
        mock_post.assert_not_called()

    @patch('src.exporters.cancel_order.time.time')
    @patch('src.exporters.cancel_order.requests.post')
    @patch('src.exporters.cancel_order.CANCEL_ORDER_MIN_CLUSTER_ID_COUNT_THRESHOLD', 5)
    def test_export_exception_handling(self, mock_post, mock_time):
        # Setup mock to raise exception and time
        mock_post.side_effect = Exception("Test exception")
        mock_time.return_value = 1744706174

        # Mock the HMAC signature generation
        with patch.object(self.exporter, '_generate_hmac_signature', return_value='test_signature'):
            # Call export method - should not raise exception
            self.exporter.export((self.request, self.result_above_threshold))

            # Verify API call was attempted
            mock_post.assert_called_once()

    def test_generate_hmac_signature(self):
        # Test the HMAC signature generation
        method = 'POST'
        path = self.api_path
        body = '{"order_id":12345,"strategy_code":"TEST_STRATEGY_CODE"}'
        timestamp = 1744706174

        # Call the method
        signature = self.exporter._generate_hmac_signature(method, path, body, timestamp)

        # Verify the signature is not empty
        self.assertTrue(signature)
        self.assertIsInstance(signature, str)
        self.assertGreater(len(signature), 0)

    def test_api_endpoint_construction(self):
        # Test that the API endpoint is constructed correctly
        self.assertEqual(self.exporter.api_endpoint, self.api_endpoint)

        # Test with different scheme, host, and path
        scheme = "http"
        host = "api.example.com"
        path = "/api/v1/test"
        expected_endpoint = f"{scheme}://{host}{path}"

        exporter = CancelOrderExporter(
            api_scheme=scheme,
            api_host=host,
            api_path=path,
            strategy_code=self.strategy_code,
            timeout_ms=self.timeout_ms,
            api_id=self.api_id,
            api_secret=self.api_secret
        )

        self.assertEqual(exporter.api_endpoint, expected_endpoint)

    @patch('src.exporters.cancel_order.requests.post')
    @patch('src.exporters.cancel_order.CANCEL_ORDER_MIN_CLUSTER_ID_COUNT_THRESHOLD', 5)
    def test_missing_configuration(self, mock_post):
        # Create exporter with missing configuration
        exporter = CancelOrderExporter(
            api_scheme=self.api_scheme,
            # Missing api_host
            api_path=self.api_path,
            strategy_code=self.strategy_code,
            timeout_ms=self.timeout_ms,
            api_id=self.api_id,
            api_secret=self.api_secret
        )

        # Call export method
        exporter.export((self.request, self.result_above_threshold))

        # Verify API was not called
        mock_post.assert_not_called()


if __name__ == '__main__':
    unittest.main()
