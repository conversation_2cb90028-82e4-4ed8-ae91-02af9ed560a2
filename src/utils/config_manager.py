import json
import os
import threading
from threading import Thread

import sccclient

from src.utils.logger import logger
from src.utils.others import cast_bool, cast_int

SPEX_CONFIG_PROJECT = "[sp]model"
SPEX_CONFIG_NAMESPACE = "ums"
SPEX_CONFIG_ITEM_KEY = "ais_vn_foood_address"

CONFIG_NAMESPACE_SECRET = os.getenv('CONFIG_NAMESPACE_SECRET')


class ConfigManager:
    _instance = None

    @staticmethod
    def get_instance():
        if ConfigManager._instance is None:
            ConfigManager._instance = ConfigLoader()
        return ConfigManager._instance


class ConfigLoader:
    def __init__(self):
        spex_config_client = sccclient.Client()

        ns, _ = spex_config_client.subscribe(
            sccclient.NamespaceIdentity(
                SPEX_CONFIG_PROJECT, SPEX_CONFIG_NAMESPACE),
            sccclient.NamespaceSecret(CONFIG_NAMESPACE_SECRET)
        )

        self.ns = ns
        self._configs = json.loads(ns.get(SPEX_CONFIG_ITEM_KEY, "{}"))
        logger.info(f"Load configs from SPEX Config: {self._configs}")

        # Initialize config values with defaults
        self._exporter_cancel_api_is_on = cast_bool(
            self._configs.get('exporter_cancel_api_is_on', False))
        self._ner_batch_size = cast_int(self._configs.get('ner_batch_size'), 3)
        self._cancel_api_strategy_code = cast_int(
            self._configs.get('cancel_api_strategy_code'), 151000058)
        self._cancel_api_id = cast_int(
            self._configs.get('cancel_api_id'), 1013)
        self._cancel_api_secret = self._configs.get(
            'cancel_api_secret', "YtYcjwHar7XJurGxWNTS")
        self._cancel_order_min_cluster_id_count_threshold = cast_int(
            self._configs.get('cancel_order_min_cluster_id_count_threshold'), 5
        )

        self._lock = threading.Lock()

        self.watch_config_changes()

    @property
    def configs(self):
        with self._lock:
            return self._configs

    @property
    def exporter_cancel_api_is_on(self):
        with self._lock:
            return self._exporter_cancel_api_is_on

    @property
    def ner_batch_size(self):
        with self._lock:
            return self._ner_batch_size

    @property
    def cancel_api_strategy_code(self):
        with self._lock:
            return self._cancel_api_strategy_code

    @property
    def cancel_api_id(self):
        with self._lock:
            return self._cancel_api_id

    @property
    def cancel_api_secret(self):
        with self._lock:
            return self._cancel_api_secret

    @property
    def cancel_order_min_cluster_id_count_threshold(self):
        with self._lock:
            return self._cancel_order_min_cluster_id_count_threshold

    def watch_config_changes(self):
        def watch():
            _, events_iterator, _ = self.ns.watch()

            for event in events_iterator:
                for iev in event.item_events:
                    if iev.key != SPEX_CONFIG_ITEM_KEY:
                        continue

                    with self._lock:
                        self._configs = json.loads(
                            event.new_snapshot.get(SPEX_CONFIG_ITEM_KEY, "{}"))

                        # Update config values
                        self._exporter_cancel_api_is_on = cast_bool(
                            self._configs.get(
                                'exporter_cancel_api_is_on', False)
                        )
                        self._ner_batch_size = cast_int(
                            self._configs.get('ner_batch_size'), 3
                        )
                        self._cancel_api_strategy_code = cast_int(
                            self._configs.get('cancel_api_strategy_code')
                        )
                        self._cancel_api_id = cast_int(self._configs.get('cancel_api_id'))
                        self._cancel_api_secret = self._configs.get('cancel_api_secret')
                        self._cancel_order_min_cluster_id_count_threshold = cast_int(
                            self._configs.get(
                                'cancel_order_min_cluster_id_count_threshold'), 5
                        )

                        logger.info(f"Updated configurations: {self._configs}")

        watcher = Thread(name="configs_watcher", target=watch)
        watcher.daemon = True
        watcher.start()
